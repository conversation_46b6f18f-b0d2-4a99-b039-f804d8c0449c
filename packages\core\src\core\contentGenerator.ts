/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
  GoogleGenAI,
} from '@google/genai';
import { DEFAULT_GEMINI_MODEL } from '../config/models.js';
import { Config } from '../config/config.js';
import { getEffectiveModel } from './modelCheck.js';
import { UserTierId } from '../code_assist/types.js';
import { ProviderFactory, ProviderAdapter, ProviderType } from '../providers/index.js';
import { EnvConfig } from '../config/env-config.js';

/**
 * Interface abstracting the core functionalities for generating content and counting tokens.
 */
export interface ContentGenerator {
  generateContent(
    request: GenerateContentParameters,
  ): Promise<GenerateContentResponse>;

  generateContentStream(
    request: GenerateContentParameters,
  ): Promise<AsyncGenerator<GenerateContentResponse>>;

  countTokens(request: CountTokensParameters): Promise<CountTokensResponse>;

  embedContent(request: EmbedContentParameters): Promise<EmbedContentResponse>;

  userTier?: UserTierId;
}

export type ContentGeneratorConfig = {
  model: string;
  apiKey?: string;
  vertexai?: boolean;
  proxy?: string | undefined;
  temperature?: number;
  maxTokens?: number;
};

export function createContentGeneratorConfig(
  config: Config,
): ContentGeneratorConfig {
  // Load environment configuration
  const envConfig = EnvConfig.getS647Config();

  // Use runtime model from config if available; otherwise, fall back to env or default
  const effectiveModel = config.getModel() || envConfig.model || DEFAULT_GEMINI_MODEL;

  const contentGeneratorConfig: ContentGeneratorConfig = {
    model: effectiveModel,
    proxy: config?.getProxy(),
  };

  // For Gemini provider, set up API key and vertex AI configuration
  const provider = config.getProvider() || envConfig.provider;
  if (provider === 'gemini') {
    const geminiApiKey = config.getApiKey() || envConfig.geminiApiKey;
    const googleCloudProject = process.env.GOOGLE_CLOUD_PROJECT;
    const googleCloudLocation = process.env.GOOGLE_CLOUD_LOCATION;

    // Determine if using Vertex AI
    const useVertexAI = process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true' ||
                       (googleCloudProject && googleCloudLocation && !geminiApiKey);

    if (useVertexAI) {
      contentGeneratorConfig.apiKey = process.env.GOOGLE_API_KEY;
      contentGeneratorConfig.vertexai = true;
    } else if (geminiApiKey) {
      contentGeneratorConfig.apiKey = geminiApiKey;
      contentGeneratorConfig.vertexai = false;
      if (contentGeneratorConfig.apiKey) {
        getEffectiveModel(
          contentGeneratorConfig.apiKey,
          contentGeneratorConfig.model,
          contentGeneratorConfig.proxy,
        );
      }
    }
  }

  return contentGeneratorConfig;
}

export async function createContentGenerator(
  config: ContentGeneratorConfig,
  gcConfig: Config,
): Promise<ContentGenerator> {
  // Initialize providers first
  await ProviderFactory.initializeProviders();

  // Get environment configuration
  const envConfig = EnvConfig.getS647Config();
  const provider = gcConfig.getProvider() || envConfig.provider;

  // Use new provider system for non-Gemini providers
  if (provider && provider !== 'gemini') {
    const providerConfig = ProviderFactory.createProviderConfig(
      provider as ProviderType,
      gcConfig.getModel(),
      {
        apiKey: gcConfig.getApiKey(),
        baseUrl: gcConfig.getBaseUrl(),
        proxy: gcConfig.getProxy(),
        temperature: config.temperature,
        maxTokens: config.maxTokens,
      }
    );

    const providerInstance = await ProviderFactory.createProvider(providerConfig);
    return new ProviderAdapter(providerInstance);
  }

  // For Gemini provider, use GoogleGenAI directly
  const version = process.env.CLI_VERSION || process.version;
  const httpOptions = {
    headers: {
      'User-Agent': `S647CLI/${version} (${process.platform}; ${process.arch})`,
    },
  };

  const googleGenAI = new GoogleGenAI({
    apiKey: config.apiKey === '' ? undefined : config.apiKey,
    vertexai: config.vertexai,
    httpOptions,
  });

  return googleGenAI.models;
}
