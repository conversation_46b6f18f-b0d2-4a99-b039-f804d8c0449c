/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import OpenAI from 'openai';
import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  ProviderResponse,
  ProviderStreamChunk,
  GenerateContentRequest,
  ProviderCapabilities,
  ProviderMessage,
  ProviderError,
  ProviderErrorType,
  ModelInfo,
  PROVIDER_MODELS,
  ProviderType,
} from './types.js';
import { ProviderFactory } from './provider-factory.js';

/**
 * OpenAI provider implementation
 */
export class OpenAIProvider extends BaseProvider {
  private client: OpenAI;

  constructor(config: ProviderConfig) {
    super(config);
    
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
      timeout: config.timeout || 30000,
      // httpAgent: config.proxy ? this.createProxyAgent(config.proxy) : undefined, // TODO: Implement proxy support
    });
  }

  protected requiresApiKey(): boolean {
    return true;
  }

  public getCapabilities(): ProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsTools: true,
      supportsImages: this.config.model.includes('gpt-4'),
      supportsSystemMessages: true,
      maxContextLength: this.getModelContextLength(),
      supportedModels: this.getSupportedModels().map(m => m.id),
    };
  }

  public getSupportedModels(): ModelInfo[] {
    return PROVIDER_MODELS[ProviderType.OPENAI];
  }

  private getModelContextLength(): number {
    const model = this.getSupportedModels().find(m => m.id === this.config.model);
    return model?.contextLength || 4096;
  }

  public async generateContent(request: GenerateContentRequest): Promise<ProviderResponse> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessages(request.messages);
        
        const response = await this.client.chat.completions.create({
          model: request.model || this.config.model,
          messages,
          max_tokens: this.normalizeMaxTokens(request.maxTokens || this.config.maxTokens),
          temperature: this.normalizeTemperature(request.temperature || this.config.temperature),
          top_p: request.topP || this.config.topP,
          frequency_penalty: request.frequencyPenalty || this.config.frequencyPenalty,
          presence_penalty: request.presencePenalty || this.config.presencePenalty,
          stop: request.stop,
          tools: request.tools,
          stream: false,
        });

        return this.convertResponse(response);
      } catch (error) {
        throw this.handleOpenAIError(error);
      }
    });
  }

  public async generateContentStream(
    request: GenerateContentRequest,
  ): Promise<AsyncGenerator<ProviderStreamChunk>> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessages(request.messages);
        
        const stream = await this.client.chat.completions.create({
          model: request.model || this.config.model,
          messages,
          max_tokens: this.normalizeMaxTokens(request.maxTokens || this.config.maxTokens),
          temperature: this.normalizeTemperature(request.temperature || this.config.temperature),
          top_p: request.topP || this.config.topP,
          frequency_penalty: request.frequencyPenalty || this.config.frequencyPenalty,
          presence_penalty: request.presencePenalty || this.config.presencePenalty,
          stop: request.stop,
          tools: request.tools,
          stream: true,
        });

        return this.convertStreamResponse(stream);
      } catch (error) {
        throw this.handleOpenAIError(error);
      }
    });
  }

  public async countTokens(content: string): Promise<number> {
    // OpenAI doesn't have a direct token counting API
    // Use a rough estimation: ~4 characters per token
    return Math.ceil(content.length / 4);
  }

  /**
   * Convert internal messages to OpenAI format
   */
  private convertMessages(messages: ProviderMessage[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map(msg => {
      if (typeof msg.content === 'string') {
        return {
          role: msg.role,
          content: msg.content,
          ...(msg.name && { name: msg.name }),
        } as OpenAI.Chat.Completions.ChatCompletionMessageParam;
      } else {
        // Handle Part[] content (for images, etc.)
        const content = msg.content.map(part => {
          if ('text' in part) {
            return { type: 'text' as const, text: part.text || '' };
          } else if ('inlineData' in part && part.inlineData) {
            return {
              type: 'image_url' as const,
              image_url: {
                url: `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`,
              },
            };
          }
          return { type: 'text' as const, text: '[Unsupported content type]' };
        });

        return {
          role: msg.role,
          content,
          ...(msg.name && { name: msg.name }),
        } as OpenAI.Chat.Completions.ChatCompletionMessageParam;
      }
    });
  }

  /**
   * Convert OpenAI response to internal format
   */
  private convertResponse(response: OpenAI.Chat.Completions.ChatCompletion): ProviderResponse {
    const choice = response.choices[0];
    const content = choice.message.content || '';
    
    return {
      content,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      } : undefined,
      finishReason: this.convertFinishReason(choice.finish_reason),
      model: response.model,
    };
  }

  /**
   * Convert OpenAI stream response to internal format
   */
  private async* convertStreamResponse(
    stream: AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>,
  ): AsyncGenerator<ProviderStreamChunk> {
    let fullContent = '';
    
    for await (const chunk of stream) {
      const choice = chunk.choices[0];
      if (!choice) continue;

      const delta = choice.delta.content || '';
      fullContent += delta;

      yield {
        content: fullContent,
        delta,
        usage: chunk.usage ? {
          promptTokens: chunk.usage.prompt_tokens,
          completionTokens: chunk.usage.completion_tokens,
          totalTokens: chunk.usage.total_tokens,
        } : undefined,
        finishReason: this.convertFinishReason(choice.finish_reason),
      };
    }
  }

  /**
   * Convert OpenAI finish reason to internal format
   */
  private convertFinishReason(finishReason: string | null): 'stop' | 'length' | 'content_filter' | 'tool_calls' | undefined {
    switch (finishReason) {
      case 'stop':
        return 'stop';
      case 'length':
        return 'length';
      case 'content_filter':
        return 'content_filter';
      case 'tool_calls':
        return 'tool_calls';
      default:
        return undefined;
    }
  }

  /**
   * Handle OpenAI-specific errors
   */
  private handleOpenAIError(error: any): ProviderError {
    if (error instanceof OpenAI.APIError) {
      const statusCode = error.status;
      const message = error.message;

      if (statusCode === 401) {
        return new ProviderError(
          ProviderErrorType.AUTHENTICATION,
          'Invalid OpenAI API key',
          statusCode,
        );
      }

      if (statusCode === 429) {
        return new ProviderError(
          ProviderErrorType.RATE_LIMIT,
          'OpenAI rate limit exceeded',
          statusCode,
          this.extractRetryAfter(error),
        );
      }

      if (statusCode === 402) {
        return new ProviderError(
          ProviderErrorType.QUOTA_EXCEEDED,
          'OpenAI quota exceeded',
          statusCode,
        );
      }

      if (statusCode === 400) {
        return new ProviderError(
          ProviderErrorType.INVALID_REQUEST,
          `OpenAI API error: ${message}`,
          statusCode,
        );
      }

      if (statusCode === 404) {
        return new ProviderError(
          ProviderErrorType.MODEL_NOT_FOUND,
          `OpenAI model not found: ${message}`,
          statusCode,
        );
      }
    }

    return this.handleError(error);
  }

  /**
   * Create proxy agent for HTTP requests
   */
  private createProxyAgent(proxy: string): any {
    // This would need to be implemented based on the proxy format
    // For now, return undefined
    return undefined;
  }
}

// Register the provider
ProviderFactory.registerProvider(ProviderType.OPENAI, OpenAIProvider);
