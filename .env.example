# S647-CLI Environment Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# AI PROVIDER CONFIGURATION
# =============================================================================

# Default AI provider to use (gemini, openai, anthropic, cohere, mistral, openai-compatible)
# If not set, defaults to 'gemini' for backward compatibility
S647_PROVIDER=gemini

# Default model for the selected provider
# Provider-specific models:
# - Gemini: gemini-2.5-pro, gemini-2.5-flash
# - OpenAI: gpt-4, gpt-4-turbo, gpt-3.5-turbo
# - Anthropic: claude-3-opus-20240229, claude-3-sonnet-20240229, claude-3-haiku-20240307
# - Cohere: command-r, command-r-plus
# - Mistral: mistral-large-latest, mistral-medium-latest, mistral-small-latest
S647_MODEL=gemini-2.5-pro

# =============================================================================
# API KEYS
# =============================================================================

# Google Gemini API Key
# Get your key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Alternative Google API Key (for backward compatibility)
GOOGLE_API_KEY=your_google_api_key_here

# OpenAI API Key
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude API Key
# Get your key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Cohere API Key
# Get your key from: https://dashboard.cohere.ai/api-keys
COHERE_API_KEY=your_cohere_api_key_here

# Mistral API Key
# Get your key from: https://console.mistral.ai/
MISTRAL_API_KEY=your_mistral_api_key_here

# =============================================================================
# OPENAI-COMPATIBLE API CONFIGURATION
# =============================================================================

# Base URL for OpenAI-compatible APIs (e.g., Ollama, LocalAI)
# Examples:
# - Ollama: http://localhost:11434/v1
# - LocalAI: http://localhost:8080/v1
# - LM Studio: http://localhost:1234/v1
S647_BASE_URL=

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================

# Enable debug mode (true/false)
S647_DEBUG=false

# Proxy configuration (if needed)
S647_PROXY=

# Maximum tokens for responses
S647_MAX_TOKENS=

# Temperature for AI responses (0.0 to 1.0)
S647_TEMPERATURE=

# Enable telemetry (true/false)
S647_TELEMETRY=true

# Enable checkpointing (true/false)
S647_CHECKPOINTING=false

# Show memory usage (true/false)
S647_SHOW_MEMORY_USAGE=false

# =============================================================================
# GOOGLE CLOUD CONFIGURATION (for Vertex AI)
# =============================================================================

# Use Vertex AI instead of Gemini API (true/false)
GOOGLE_GENAI_USE_VERTEXAI=false

# Google Cloud Project ID (required for Vertex AI)
GOOGLE_CLOUD_PROJECT=your_project_id

# Google Cloud Location (required for Vertex AI)
GOOGLE_CLOUD_LOCATION=us-central1

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# File filtering options
S647_RESPECT_GITIGNORE=true
S647_RESPECT_GEMINI_IGNORE=true
S647_ENABLE_RECURSIVE_FILE_SEARCH=true

# Session configuration
S647_MAX_SESSION_TURNS=

# IDE mode (true/false)
S647_IDE_MODE=false

# Experimental features
S647_EXPERIMENTAL_ACP=false

# =============================================================================
# USAGE EXAMPLES
# =============================================================================

# Example 1: Use OpenAI GPT-4
# S647_PROVIDER=openai
# S647_MODEL=gpt-4
# OPENAI_API_KEY=sk-your-openai-key

# Example 2: Use Anthropic Claude
# S647_PROVIDER=anthropic
# S647_MODEL=claude-3-sonnet-20240229
# ANTHROPIC_API_KEY=your-anthropic-key

# Example 3: Use local Ollama
# S647_PROVIDER=openai-compatible
# S647_MODEL=llama2
# S647_BASE_URL=http://localhost:11434/v1
# OPENAI_API_KEY=dummy-key-for-ollama

# Example 4: Use Cohere with custom settings
# S647_PROVIDER=cohere
# S647_MODEL=command-r-plus
# COHERE_API_KEY=your-cohere-key
# S647_TEMPERATURE=0.7
# S647_MAX_TOKENS=2000

# Example 5: Use Mistral AI
# S647_PROVIDER=mistral
# S647_MODEL=mistral-large-latest
# MISTRAL_API_KEY=your-mistral-key
# S647_TEMPERATURE=0.7
