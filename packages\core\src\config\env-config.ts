/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { config } from 'dotenv';
import { existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

/**
 * Environment configuration utility
 */
export class EnvConfig {
  private static loaded = false;
  private static envValues: Record<string, string> = {};

  /**
   * Load .env file from project root or specified path
   */
  public static load(envPath?: string): void {
    if (this.loaded) return;

    // Get current file directory for ES modules
    const currentDir = dirname(fileURLToPath(import.meta.url));

    // Try to find .env file in various locations
    const possiblePaths = [
      envPath,
      '.env',
      join(process.cwd(), '.env'),
      join(currentDir, '../../../../.env'), // From packages/core/src/config to root
      join(currentDir, '../../../../../.env'), // Alternative path
    ].filter(Boolean) as string[];

    let envFile: string | undefined;
    for (const path of possiblePaths) {
      if (existsSync(path)) {
        envFile = path;
        break;
      }
    }

    if (envFile) {
      const result = config({ path: envFile });
      if (result.parsed) {
        this.envValues = { ...result.parsed };
        console.log(`📄 Loaded environment configuration from: ${envFile}`);
      }
    }

    this.loaded = true;
  }

  /**
   * Get environment value with fallback
   */
  public static get(key: string, fallback?: string): string | undefined {
    this.load(); // Ensure .env is loaded
    
    // Priority: process.env > .env file > fallback
    return process.env[key] || this.envValues[key] || fallback;
  }

  /**
   * Get boolean environment value
   */
  public static getBoolean(key: string, fallback = false): boolean {
    const value = this.get(key);
    if (value === undefined) return fallback;
    
    return value.toLowerCase() === 'true' || value === '1';
  }

  /**
   * Get number environment value
   */
  public static getNumber(key: string, fallback?: number): number | undefined {
    const value = this.get(key);
    if (value === undefined) return fallback;
    
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? fallback : parsed;
  }

  /**
   * Get float environment value
   */
  public static getFloat(key: string, fallback?: number): number | undefined {
    const value = this.get(key);
    if (value === undefined) return fallback;
    
    const parsed = parseFloat(value);
    return isNaN(parsed) ? fallback : parsed;
  }

  /**
   * Check if environment value exists
   */
  public static has(key: string): boolean {
    return this.get(key) !== undefined;
  }

  /**
   * Get all environment values (for debugging)
   */
  public static getAll(): Record<string, string> {
    this.load();
    return { ...this.envValues };
  }

  /**
   * Reset loaded state (for testing)
   */
  public static reset(): void {
    this.loaded = false;
    this.envValues = {};
  }

  /**
   * Get S647-specific configuration values
   */
  public static getS647Config() {
    this.load();
    
    return {
      // Provider configuration
      provider: this.get('S647_PROVIDER', 'gemini'),
      model: this.get('S647_MODEL'),
      baseUrl: this.get('S647_BASE_URL'),
      
      // API Keys
      geminiApiKey: this.get('GEMINI_API_KEY') || this.get('GOOGLE_API_KEY'),
      openaiApiKey: this.get('OPENAI_API_KEY'),
      anthropicApiKey: this.get('ANTHROPIC_API_KEY'),
      cohereApiKey: this.get('COHERE_API_KEY'),
      mistralApiKey: this.get('MISTRAL_API_KEY'),
      
      // General configuration
      debug: this.getBoolean('S647_DEBUG', false),
      proxy: this.get('S647_PROXY'),
      maxTokens: this.getNumber('S647_MAX_TOKENS'),
      temperature: this.getFloat('S647_TEMPERATURE'),
      telemetry: this.getBoolean('S647_TELEMETRY', true),
      checkpointing: this.getBoolean('S647_CHECKPOINTING', false),
      showMemoryUsage: this.getBoolean('S647_SHOW_MEMORY_USAGE', false),
      
      // Google Cloud configuration
      useVertexAI: this.getBoolean('GOOGLE_GENAI_USE_VERTEXAI', false),
      googleCloudProject: this.get('GOOGLE_CLOUD_PROJECT'),
      googleCloudLocation: this.get('GOOGLE_CLOUD_LOCATION'),
      
      // File filtering
      respectGitIgnore: this.getBoolean('S647_RESPECT_GITIGNORE', true),
      respectGeminiIgnore: this.getBoolean('S647_RESPECT_GEMINI_IGNORE', true),
      enableRecursiveFileSearch: this.getBoolean('S647_ENABLE_RECURSIVE_FILE_SEARCH', true),
      
      // Advanced configuration
      maxSessionTurns: this.getNumber('S647_MAX_SESSION_TURNS'),
      ideMode: this.getBoolean('S647_IDE_MODE', false),
      experimentalAcp: this.getBoolean('S647_EXPERIMENTAL_ACP', false),
    };
  }

  /**
   * Get API key for a specific provider
   */
  public static getApiKeyForProvider(provider: string): string | undefined {
    this.load();
    
    switch (provider.toLowerCase()) {
      case 'gemini':
        return this.get('GEMINI_API_KEY') || this.get('GOOGLE_API_KEY');
      case 'openai':
      case 'openai-compatible':
        return this.get('OPENAI_API_KEY');
      case 'anthropic':
        return this.get('ANTHROPIC_API_KEY');
      case 'cohere':
        return this.get('COHERE_API_KEY');
      case 'mistral':
        return this.get('MISTRAL_API_KEY');
      default:
        return undefined;
    }
  }

  /**
   * Validate required environment variables for a provider
   */
  public static validateProviderConfig(provider: string): string[] {
    const errors: string[] = [];
    const apiKey = this.getApiKeyForProvider(provider);
    
    if (!apiKey && provider !== 'openai-compatible') {
      const keyName = this.getApiKeyNameForProvider(provider);
      errors.push(`Missing API key for ${provider} provider. Please set ${keyName} in your .env file.`);
    }
    
    if (provider === 'openai-compatible') {
      const baseUrl = this.get('S647_BASE_URL');
      if (!baseUrl) {
        errors.push('Missing base URL for OpenAI-compatible provider. Please set S647_BASE_URL in your .env file.');
      }
    }
    
    return errors;
  }

  /**
   * Get the environment variable name for a provider's API key
   */
  private static getApiKeyNameForProvider(provider: string): string {
    switch (provider.toLowerCase()) {
      case 'gemini':
        return 'GEMINI_API_KEY or GOOGLE_API_KEY';
      case 'openai':
        return 'OPENAI_API_KEY';
      case 'anthropic':
        return 'ANTHROPIC_API_KEY';
      case 'cohere':
        return 'COHERE_API_KEY';
      case 'mistral':
        return 'MISTRAL_API_KEY';
      default:
        return `${provider.toUpperCase()}_API_KEY`;
    }
  }

  /**
   * Print configuration summary (for debugging)
   */
  public static printConfigSummary(): void {
    const config = this.getS647Config();
    
    console.log('\n🔧 S647-CLI Configuration Summary:');
    console.log(`   Provider: ${config.provider}`);
    console.log(`   Model: ${config.model || 'default'}`);
    console.log(`   Base URL: ${config.baseUrl || 'default'}`);
    console.log(`   Debug: ${config.debug}`);
    console.log(`   Telemetry: ${config.telemetry}`);
    
    // Show which API keys are configured (without revealing the keys)
    const apiKeys = [
      { name: 'Gemini', key: config.geminiApiKey },
      { name: 'OpenAI', key: config.openaiApiKey },
      { name: 'Anthropic', key: config.anthropicApiKey },
      { name: 'Cohere', key: config.cohereApiKey },
      { name: 'Mistral', key: config.mistralApiKey },
    ];
    
    const configuredKeys = apiKeys.filter(k => k.key).map(k => k.name);
    if (configuredKeys.length > 0) {
      console.log(`   Configured API Keys: ${configuredKeys.join(', ')}`);
    } else {
      console.log('   ⚠️  No API keys configured');
    }
    console.log('');
  }
}
