/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import Anthropic from '@anthropic-ai/sdk';
import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  ProviderResponse,
  ProviderStreamChunk,
  GenerateContentRequest,
  ProviderCapabilities,
  ProviderMessage,
  ProviderError,
  ProviderErrorType,
  ModelInfo,
  PROVIDER_MODELS,
  ProviderType,
} from './types.js';
import { ProviderFactory } from './provider-factory.js';

/**
 * Anthropic Claude provider implementation
 */
export class AnthropicProvider extends BaseProvider {
  private client: Anthropic;

  constructor(config: ProviderConfig) {
    super(config);
    
    this.client = new Anthropic({
      apiKey: config.apiKey,
      timeout: config.timeout || 30000,
    });
  }

  protected requiresApiKey(): boolean {
    return true;
  }

  public getCapabilities(): ProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsTools: true,
      supportsImages: true,
      supportsSystemMessages: true,
      maxContextLength: this.getModelContextLength(),
      supportedModels: this.getSupportedModels().map(m => m.id),
    };
  }

  public getSupportedModels(): ModelInfo[] {
    return PROVIDER_MODELS[ProviderType.ANTHROPIC];
  }

  private getModelContextLength(): number {
    const model = this.getSupportedModels().find(m => m.id === this.config.model);
    return model?.contextLength || 200000;
  }

  public async generateContent(request: GenerateContentRequest): Promise<ProviderResponse> {
    return this.withRetry(async () => {
      try {
        const { messages, systemMessage } = this.convertMessages(request.messages);
        
        const response = await this.client.messages.create({
          model: request.model || this.config.model,
          messages,
          system: systemMessage,
          max_tokens: this.normalizeMaxTokens(request.maxTokens || this.config.maxTokens) || 4096,
          temperature: this.normalizeTemperature(request.temperature || this.config.temperature),
          top_p: request.topP || this.config.topP,
          stop_sequences: request.stop,
          stream: false,
        });

        return this.convertResponse(response);
      } catch (error) {
        throw this.handleAnthropicError(error);
      }
    });
  }

  public async generateContentStream(
    request: GenerateContentRequest,
  ): Promise<AsyncGenerator<ProviderStreamChunk>> {
    return this.withRetry(async () => {
      try {
        const { messages, systemMessage } = this.convertMessages(request.messages);
        
        const stream = await this.client.messages.create({
          model: request.model || this.config.model,
          messages,
          system: systemMessage,
          max_tokens: this.normalizeMaxTokens(request.maxTokens || this.config.maxTokens) || 4096,
          temperature: this.normalizeTemperature(request.temperature || this.config.temperature),
          top_p: request.topP || this.config.topP,
          stop_sequences: request.stop,
          stream: true,
        });

        return this.convertStreamResponse(stream);
      } catch (error) {
        throw this.handleAnthropicError(error);
      }
    });
  }

  public async countTokens(content: string): Promise<number> {
    // Anthropic doesn't have a direct token counting API
    // Use a rough estimation: ~4 characters per token
    return Math.ceil(content.length / 4);
  }

  /**
   * Convert internal messages to Anthropic format
   */
  private convertMessages(messages: ProviderMessage[]): {
    messages: Anthropic.Messages.MessageParam[];
    systemMessage?: string;
  } {
    let systemMessage: string | undefined;
    const anthropicMessages: Anthropic.Messages.MessageParam[] = [];

    for (const msg of messages) {
      if (msg.role === 'system') {
        // Anthropic handles system messages separately
        systemMessage = typeof msg.content === 'string' ? msg.content : 
          msg.content.map(part => ('text' in part ? part.text || '' : '')).join('');
        continue;
      }

      if (typeof msg.content === 'string') {
        anthropicMessages.push({
          role: msg.role === 'assistant' ? 'assistant' : 'user',
          content: msg.content,
        });
      } else {
        // Handle Part[] content (for images, etc.)
        const content = msg.content.map(part => {
          if ('text' in part) {
            return { type: 'text' as const, text: part.text || '' };
          } else if ('inlineData' in part && part.inlineData && part.inlineData.data) {
            return {
              type: 'image' as const,
              source: {
                type: 'base64' as const,
                media_type: part.inlineData.mimeType as any,
                data: part.inlineData.data,
              },
            };
          }
          return { type: 'text' as const, text: '[Unsupported content type]' };
        });

        anthropicMessages.push({
          role: msg.role === 'assistant' ? 'assistant' : 'user',
          content,
        });
      }
    }

    return { messages: anthropicMessages, systemMessage };
  }

  /**
   * Convert Anthropic response to internal format
   */
  private convertResponse(response: Anthropic.Messages.Message): ProviderResponse {
    const content = response.content
      .map(block => block.type === 'text' ? block.text : '')
      .join('');
    
    return {
      content,
      usage: response.usage ? {
        promptTokens: response.usage.input_tokens,
        completionTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens,
      } : undefined,
      finishReason: this.convertFinishReason(response.stop_reason),
      model: response.model,
    };
  }

  /**
   * Convert Anthropic stream response to internal format
   */
  private async* convertStreamResponse(
    stream: AsyncIterable<Anthropic.Messages.MessageStreamEvent>,
  ): AsyncGenerator<ProviderStreamChunk> {
    let fullContent = '';
    let usage: any = undefined;
    
    for await (const event of stream) {
      if (event.type === 'content_block_delta' && event.delta.type === 'text_delta') {
        const delta = event.delta.text;
        fullContent += delta;

        yield {
          content: fullContent,
          delta,
          usage,
          finishReason: undefined,
        };
      } else if (event.type === 'message_delta' && event.delta.stop_reason) {
        yield {
          content: fullContent,
          delta: '',
          usage,
          finishReason: this.convertFinishReason(event.delta.stop_reason),
        };
      } else if (event.type === 'message_delta' && event.usage) {
        usage = {
          promptTokens: event.usage.input_tokens || 0,
          completionTokens: event.usage.output_tokens || 0,
          totalTokens: (event.usage.input_tokens || 0) + (event.usage.output_tokens || 0),
        };
      }
    }
  }

  /**
   * Convert Anthropic finish reason to internal format
   */
  private convertFinishReason(finishReason: string | null): 'stop' | 'length' | 'content_filter' | 'tool_calls' | undefined {
    switch (finishReason) {
      case 'end_turn':
        return 'stop';
      case 'max_tokens':
        return 'length';
      case 'stop_sequence':
        return 'stop';
      case 'tool_use':
        return 'tool_calls';
      default:
        return undefined;
    }
  }

  /**
   * Handle Anthropic-specific errors
   */
  private handleAnthropicError(error: any): ProviderError {
    if (error instanceof Anthropic.APIError) {
      const statusCode = error.status;
      const message = error.message;

      if (statusCode === 401) {
        return new ProviderError(
          ProviderErrorType.AUTHENTICATION,
          'Invalid Anthropic API key',
          statusCode,
        );
      }

      if (statusCode === 429) {
        return new ProviderError(
          ProviderErrorType.RATE_LIMIT,
          'Anthropic rate limit exceeded',
          statusCode,
          this.extractRetryAfter(error),
        );
      }

      if (statusCode === 402) {
        return new ProviderError(
          ProviderErrorType.QUOTA_EXCEEDED,
          'Anthropic quota exceeded',
          statusCode,
        );
      }

      if (statusCode === 400) {
        return new ProviderError(
          ProviderErrorType.INVALID_REQUEST,
          `Anthropic API error: ${message}`,
          statusCode,
        );
      }

      if (statusCode === 404) {
        return new ProviderError(
          ProviderErrorType.MODEL_NOT_FOUND,
          `Anthropic model not found: ${message}`,
          statusCode,
        );
      }
    }

    return this.handleError(error);
  }
}

// Register the provider
ProviderFactory.registerProvider(ProviderType.ANTHROPIC, AnthropicProvider);
